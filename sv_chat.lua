RegisterServerEvent('chat:init')
RegisterServerEvent('chat:addTemplate')
RegisterServerEvent('chat:addMessage')
RegisterServerEvent('chat:addSuggestion')
RegisterServerEvent('chat:removeSuggestion')
RegisterServerEvent('_chat:messageEntered')
RegisterServerEvent('chat:clear')
RegisterServerEvent('__cfx_internal:commandFallback')

-- Handle regular chat messages and print to server console
AddEventHandler('chatMessage', function(source, name, message)
    -- Print to server console (txAdmin console)
    print("[CHAT] " .. name .. ": " .. message)
end)

-- Handle custom x_ chat messages and print to server console
RegisterNetEvent('x_:chatMessage')
AddEventHandler('x_:chatMessage', function(title, author, color, text, systemMessage, systemColour)
    local name = GetPlayerName(source)
    local consoleMsg = "[CHAT]"
    if title and title ~= "" then
        consoleMsg = consoleMsg .. " [" .. title .. "]"
    end
    consoleMsg = consoleMsg .. " " .. name .. ": " .. text
    print(consoleMsg)
end)


AddEventHandler('__cfx_internal:commandFallback', function(command)
    local name = GetPlayerName(source)

    -- Print command to server console
    print("[CHAT] " .. name .. " executed: /" .. command)

    TriggerEvent('chatMessage', source, name, '/' .. command)

    if not WasEventCanceled() then
        TriggerClientEvent('chatMessage', -1, name, { 255, 255, 255 }, '' .. command)
    end

    CancelEvent()
end)

-- command suggestions for clients
local function refreshCommands(player)
    if GetRegisteredCommands then
        local registeredCommands = GetRegisteredCommands()

        local suggestions = {}

        for _, command in ipairs(registeredCommands) do
            if IsPlayerAceAllowed(player, ('command.%s'):format(command.name)) then
                table.insert(suggestions, {
                    name = '/' .. command.name,
                    help = ''
                })
            end
        end

        TriggerClientEvent('chat:addSuggestions', player, suggestions)
    end
end

AddEventHandler('chat:init', function()
    refreshCommands(source)
end)

AddEventHandler('onServerResourceStart', function(resName)
    Wait(500)

    for _, player in ipairs(GetPlayers()) do
        refreshCommands(player)
    end
end)
